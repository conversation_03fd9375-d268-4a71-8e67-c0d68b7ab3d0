@extends('layouts.app')

@section('title', 'Tambah Peminjaman')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h4>Tambah Peminjaman Baru</h4>
        <div class="card-header-action">
          <a href="{{ route('borrowings.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
          </a>
        </div>
      </div>
      <div class="card-body">
        <form action="{{ route('borrowings.store') }}" method="POST">
          @csrf
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="member_id">Anggota <span class="text-danger">*</span></label>
                <select class="form-control select2 @error('member_id') is-invalid @enderror" 
                        id="member_id" name="member_id" required>
                  <option value=""><PERSON>lih <PERSON></option>
                  @foreach($members as $member)
                    <option value="{{ $member->id }}" 
                            data-nim="{{ $member->nim_nip }}" 
                            data-status="{{ $member->status }}"
                            data-expired="{{ $member->expired_date->format('d/m/Y') }}"
                            {{ old('member_id') == $member->id ? 'selected' : '' }}>
                      {{ $member->name }} ({{ $member->member_id }})
                    </option>
                  @endforeach
                </select>
                @error('member_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div id="member-info" class="mt-2" style="display: none;">
                  <small class="text-muted">
                    <strong>NIM/NIP:</strong> <span id="member-nim"></span><br>
                    <strong>Status:</strong> <span id="member-status"></span><br>
                    <strong>Berlaku sampai:</strong> <span id="member-expired"></span>
                  </small>
                </div>
              </div>

              <div class="form-group">
                <label for="book_id">Buku <span class="text-danger">*</span></label>
                <select class="form-control select2 @error('book_id') is-invalid @enderror" 
                        id="book_id" name="book_id" required>
                  <option value="">Pilih Buku</option>
                  @foreach($books as $book)
                    <option value="{{ $book->id }}" 
                            data-author="{{ $book->author }}" 
                            data-isbn="{{ $book->isbn }}"
                            data-category="{{ $book->category->name }}"
                            data-available="{{ $book->available_copies }}"
                            data-location="{{ $book->shelf_location }}"
                            {{ old('book_id') == $book->id ? 'selected' : '' }}>
                      {{ $book->title }} (Tersedia: {{ $book->available_copies }})
                    </option>
                  @endforeach
                </select>
                @error('book_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div id="book-info" class="mt-2" style="display: none;">
                  <small class="text-muted">
                    <strong>Pengarang:</strong> <span id="book-author"></span><br>
                    <strong>ISBN:</strong> <span id="book-isbn"></span><br>
                    <strong>Kategori:</strong> <span id="book-category"></span><br>
                    <strong>Lokasi:</strong> <span id="book-location"></span><br>
                    <strong>Tersedia:</strong> <span id="book-available"></span> eksemplar
                  </small>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <label for="borrow_date">Tanggal Pinjam <span class="text-danger">*</span></label>
                <input type="date" class="form-control @error('borrow_date') is-invalid @enderror" 
                       id="borrow_date" name="borrow_date" value="{{ old('borrow_date', date('Y-m-d')) }}" required>
                @error('borrow_date')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="form-group">
                <label for="due_date">Jatuh Tempo <span class="text-danger">*</span></label>
                <input type="date" class="form-control @error('due_date') is-invalid @enderror" 
                       id="due_date" name="due_date" value="{{ old('due_date', date('Y-m-d', strtotime('+7 days'))) }}" required>
                @error('due_date')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text text-muted">Default: 7 hari dari tanggal pinjam</small>
              </div>

              <div class="form-group">
                <label for="notes">Catatan</label>
                <textarea class="form-control @error('notes') is-invalid @enderror" 
                          id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                @error('notes')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
          </div>

          <div class="form-group">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> Simpan Peminjaman
            </button>
            <a href="{{ route('borrowings.index') }}" class="btn btn-secondary">
              <i class="fas fa-times"></i> Batal
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@endsection

@push('style')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css" rel="stylesheet" />
@endpush

@push('script')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
  // Initialize Select2
  $('.select2').select2({
    theme: 'bootstrap4',
    width: '100%'
  });

  // Show member info when selected
  $('#member_id').change(function() {
    let selectedOption = $(this).find('option:selected');
    if (selectedOption.val()) {
      $('#member-nim').text(selectedOption.data('nim'));
      $('#member-status').text(selectedOption.data('status'));
      $('#member-expired').text(selectedOption.data('expired'));
      $('#member-info').show();
    } else {
      $('#member-info').hide();
    }
  });

  // Show book info when selected
  $('#book_id').change(function() {
    let selectedOption = $(this).find('option:selected');
    if (selectedOption.val()) {
      $('#book-author').text(selectedOption.data('author'));
      $('#book-isbn').text(selectedOption.data('isbn'));
      $('#book-category').text(selectedOption.data('category'));
      $('#book-location').text(selectedOption.data('location') || '-');
      $('#book-available').text(selectedOption.data('available'));
      $('#book-info').show();
    } else {
      $('#book-info').hide();
    }
  });

  // Auto calculate due date
  $('#borrow_date').change(function() {
    let borrowDate = new Date($(this).val());
    let dueDate = new Date(borrowDate);
    dueDate.setDate(dueDate.getDate() + 7);
    $('#due_date').val(dueDate.toISOString().split('T')[0]);
  });

  // Trigger change events if values are already selected
  $('#member_id').trigger('change');
  $('#book_id').trigger('change');
});
</script>
@endpush
