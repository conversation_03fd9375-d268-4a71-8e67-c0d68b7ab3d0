<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\MemberController;
use App\Http\Controllers\BorrowingController;
use App\Http\Controllers\ReturnController;
use App\Http\Controllers\FineController;
use App\Http\Controllers\ReportController;

Route::get('/', function () {
    return redirect()->route('login');
});

Auth::routes(['register' => false]); // Disable registration

// Protected routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Categories
    Route::middleware(['permission:manage-books'])->group(function () {
        Route::resource('categories', CategoryController::class);
    });

    // Books
    Route::middleware(['permission:manage-books'])->group(function () {
        Route::resource('books', BookController::class);
        Route::get('books/{book}/qr', [BookController::class, 'generateQr'])->name('books.generateQr');
    });

    // Members
    Route::middleware(['permission:manage-members'])->group(function () {
        Route::resource('members', MemberController::class);
        Route::get('members/{member}/card', [MemberController::class, 'generateCard'])->name('members.generateCard');
        Route::get('members/{member}/qr', [MemberController::class, 'generateQr'])->name('members.generateQr');
    });

    // Borrowings
    Route::middleware(['permission:manage-borrowings'])->group(function () {
        Route::resource('borrowings', BorrowingController::class);
        Route::post('borrowings/{borrowing}/extend', [BorrowingController::class, 'extend'])->name('borrowings.extend');
        Route::post('borrowings/update-overdue', [BorrowingController::class, 'updateOverdueStatus'])->name('borrowings.updateOverdue');
    });

    // Returns
    Route::middleware(['permission:manage-returns'])->group(function () {
        Route::resource('returns', ReturnController::class);
        Route::post('returns/quick-return', [ReturnController::class, 'quickReturn'])->name('returns.quickReturn');
    });

    // Fines
    Route::middleware(['permission:manage-fines'])->group(function () {
        Route::resource('fines', FineController::class);
    });

    // Reports
    Route::middleware(['permission:view-reports'])->group(function () {
        Route::get('reports', [ReportController::class, 'index'])->name('reports.index');
        Route::get('reports/books', [ReportController::class, 'books'])->name('reports.books');
        Route::get('reports/borrowings', [ReportController::class, 'borrowings'])->name('reports.borrowings');
        Route::get('reports/fines', [ReportController::class, 'fines'])->name('reports.fines');
    });
});

// Redirect /home to dashboard
Route::get('/home', function () {
    return redirect()->route('dashboard');
});
