@extends('layouts.app')

@section('title', 'Tambah Pengembalian')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h4>Proses Pengembalian Buku</h4>
        <div class="card-header-action">
          <a href="{{ route('returns.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
          </a>
        </div>
      </div>
      <div class="card-body">
        <form action="{{ route('returns.store') }}" method="POST">
          @csrf
          
          <div class="row">
            <div class="col-md-8">
              <div class="form-group">
                <label for="borrowing_id">Peminjaman <span class="text-danger">*</span></label>
                <select class="form-control select2 @error('borrowing_id') is-invalid @enderror" 
                        id="borrowing_id" name="borrowing_id" required>
                  <option value=""><PERSON><PERSON><PERSON></option>
                  @foreach($activeBorrowings as $borrowing)
                    <option value="{{ $borrowing->id }}" 
                            data-member="{{ $borrowing->member->name }}"
                            data-member-id="{{ $borrowing->member->member_id }}"
                            data-book="{{ $borrowing->book->title }}"
                            data-author="{{ $borrowing->book->author }}"
                            data-borrow-date="{{ $borrowing->borrow_date->format('d/m/Y') }}"
                            data-due-date="{{ $borrowing->due_date->format('d/m/Y') }}"
                            data-days-late="{{ $borrowing->due_date < now() ? $borrowing->due_date->diffInDays(now()) : 0 }}"
                            data-is-overdue="{{ $borrowing->due_date < now() ? 'true' : 'false' }}"
                            {{ old('borrowing_id') == $borrowing->id ? 'selected' : '' }}>
                      {{ $borrowing->borrowing_code }} - {{ $borrowing->member->name }} - {{ $borrowing->book->title }}
                      @if($borrowing->due_date < now())
                        (TERLAMBAT {{ $borrowing->due_date->diffInDays(now()) }} hari)
                      @endif
                    </option>
                  @endforeach
                </select>
                @error('borrowing_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div id="borrowing-details" class="alert alert-info" style="display: none;">
                <h6>Detail Peminjaman:</h6>
                <div class="row">
                  <div class="col-md-6">
                    <strong>Anggota:</strong> <span id="detail-member"></span><br>
                    <strong>ID Anggota:</strong> <span id="detail-member-id"></span><br>
                    <strong>Buku:</strong> <span id="detail-book"></span><br>
                    <strong>Pengarang:</strong> <span id="detail-author"></span>
                  </div>
                  <div class="col-md-6">
                    <strong>Tanggal Pinjam:</strong> <span id="detail-borrow-date"></span><br>
                    <strong>Jatuh Tempo:</strong> <span id="detail-due-date"></span><br>
                    <strong>Status:</strong> <span id="detail-status"></span><br>
                    <strong>Keterlambatan:</strong> <span id="detail-late"></span>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="return_date">Tanggal Pengembalian <span class="text-danger">*</span></label>
                    <input type="date" class="form-control @error('return_date') is-invalid @enderror" 
                           id="return_date" name="return_date" value="{{ old('return_date', date('Y-m-d')) }}" required>
                    @error('return_date')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="condition">Kondisi Buku <span class="text-danger">*</span></label>
                    <select class="form-control @error('condition') is-invalid @enderror" 
                            id="condition" name="condition" required>
                      <option value="">Pilih Kondisi</option>
                      <option value="good" {{ old('condition') == 'good' ? 'selected' : '' }}>Baik</option>
                      <option value="damaged" {{ old('condition') == 'damaged' ? 'selected' : '' }}>Rusak</option>
                      <option value="lost" {{ old('condition') == 'lost' ? 'selected' : '' }}>Hilang</option>
                    </select>
                    @error('condition')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="form-group">
                <label for="notes">Catatan</label>
                <textarea class="form-control @error('notes') is-invalid @enderror" 
                          id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                @error('notes')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="col-md-4">
              <div class="card">
                <div class="card-header">
                  <h6>Perhitungan Denda</h6>
                </div>
                <div class="card-body">
                  <div id="fine-calculation" style="display: none;">
                    <table class="table table-sm">
                      <tr>
                        <td>Keterlambatan:</td>
                        <td class="text-right">
                          <span id="late-days">0</span> hari × Rp 1.000 = 
                          <strong>Rp <span id="late-fine">0</span></strong>
                        </td>
                      </tr>
                      <tr id="condition-fine-row" style="display: none;">
                        <td>Kondisi buku:</td>
                        <td class="text-right">
                          <strong>Rp <span id="condition-fine">0</span></strong>
                        </td>
                      </tr>
                      <tr class="border-top">
                        <td><strong>Total Denda:</strong></td>
                        <td class="text-right">
                          <strong class="text-danger">Rp <span id="total-fine">0</span></strong>
                        </td>
                      </tr>
                    </table>
                  </div>
                  <p class="text-muted" id="no-fine-message">Pilih peminjaman untuk melihat perhitungan denda</p>
                </div>
              </div>

              <div class="card mt-3">
                <div class="card-header">
                  <h6>Informasi Denda</h6>
                </div>
                <div class="card-body">
                  <small class="text-muted">
                    <strong>Keterlambatan:</strong> Rp 1.000 per hari<br>
                    <strong>Buku rusak:</strong> Rp 10.000<br>
                    <strong>Buku hilang:</strong> Rp 50.000<br><br>
                    <em>Denda akan otomatis dibuat jika ada keterlambatan atau kerusakan buku.</em>
                  </small>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> Proses Pengembalian
            </button>
            <a href="{{ route('returns.index') }}" class="btn btn-secondary">
              <i class="fas fa-times"></i> Batal
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@endsection

@push('style')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css" rel="stylesheet" />
@endpush

@push('script')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
  // Initialize Select2
  $('.select2').select2({
    theme: 'bootstrap4',
    width: '100%'
  });

  // Show borrowing details when selected
  $('#borrowing_id').change(function() {
    let selectedOption = $(this).find('option:selected');
    if (selectedOption.val()) {
      $('#detail-member').text(selectedOption.data('member'));
      $('#detail-member-id').text(selectedOption.data('member-id'));
      $('#detail-book').text(selectedOption.data('book'));
      $('#detail-author').text(selectedOption.data('author'));
      $('#detail-borrow-date').text(selectedOption.data('borrow-date'));
      $('#detail-due-date').text(selectedOption.data('due-date'));
      
      let isOverdue = selectedOption.data('is-overdue');
      let daysLate = selectedOption.data('days-late');
      
      if (isOverdue) {
        $('#detail-status').html('<span class="badge badge-danger">Terlambat</span>');
        $('#detail-late').html('<span class="text-danger">' + daysLate + ' hari</span>');
      } else {
        $('#detail-status').html('<span class="badge badge-warning">Dipinjam</span>');
        $('#detail-late').text('Tidak terlambat');
      }
      
      $('#borrowing-details').show();
      calculateFine();
    } else {
      $('#borrowing-details').hide();
      $('#fine-calculation').hide();
      $('#no-fine-message').show();
    }
  });

  // Calculate fine when condition or return date changes
  $('#condition, #return_date').change(function() {
    calculateFine();
  });

  function calculateFine() {
    let selectedOption = $('#borrowing_id').find('option:selected');
    if (!selectedOption.val()) return;
    
    let returnDate = new Date($('#return_date').val());
    let dueDate = new Date(selectedOption.data('due-date').split('/').reverse().join('-'));
    let condition = $('#condition').val();
    
    // Calculate late days
    let lateDays = returnDate > dueDate ? Math.ceil((returnDate - dueDate) / (1000 * 60 * 60 * 24)) : 0;
    let lateFine = lateDays * 1000;
    
    // Calculate condition fine
    let conditionFine = 0;
    if (condition === 'damaged') {
      conditionFine = 10000;
    } else if (condition === 'lost') {
      conditionFine = 50000;
    }
    
    let totalFine = lateFine + conditionFine;
    
    // Update display
    $('#late-days').text(lateDays);
    $('#late-fine').text(lateFine.toLocaleString('id-ID'));
    $('#condition-fine').text(conditionFine.toLocaleString('id-ID'));
    $('#total-fine').text(totalFine.toLocaleString('id-ID'));
    
    if (condition && (condition === 'damaged' || condition === 'lost')) {
      $('#condition-fine-row').show();
    } else {
      $('#condition-fine-row').hide();
    }
    
    $('#fine-calculation').show();
    $('#no-fine-message').hide();
  }

  // Trigger change events if values are already selected
  $('#borrowing_id').trigger('change');
});
</script>
@endpush
