[2025-06-08 05:03:20] local.ERROR: SQLSTATE[HY000] [1049] Unknown database 'sipus' (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'sipus' (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(67): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\FreshCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'sipus' at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(67): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\FreshCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}
"} 
[2025-06-08 05:09:40] local.ERROR: Call to undefined method App\Http\Controllers\Auth\LoginController::middleware() {"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Auth\\LoginController::middleware() at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\app\\Http\\Controllers\\Auth\\LoginController.php:37)
[stacktrace]
#0 [internal function]: App\\Http\\Controllers\\Auth\\LoginController->__construct()
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#55 {main}
"} 
[2025-06-08 05:09:41] local.ERROR: Call to undefined method App\Http\Controllers\Auth\LoginController::middleware() {"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Auth\\LoginController::middleware() at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\app\\Http\\Controllers\\Auth\\LoginController.php:37)
[stacktrace]
#0 [internal function]: App\\Http\\Controllers\\Auth\\LoginController->__construct()
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#55 {main}
"} 
[2025-06-08 05:11:30] local.ERROR: Call to undefined method App\Http\Controllers\Auth\LoginController::middleware() {"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Auth\\LoginController::middleware() at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\app\\Http\\Controllers\\Auth\\LoginController.php:37)
[stacktrace]
#0 [internal function]: App\\Http\\Controllers\\Auth\\LoginController->__construct()
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#55 {main}
"} 
[2025-06-08 05:11:54] local.ERROR: Call to undefined method App\Http\Controllers\Auth\LoginController::middleware() {"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Auth\\LoginController::middleware() at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\app\\Http\\Controllers\\Auth\\LoginController.php:37)
[stacktrace]
#0 [internal function]: App\\Http\\Controllers\\Auth\\LoginController->__construct()
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}
"} 
[2025-06-08 05:12:02] local.ERROR: Call to undefined method App\Http\Controllers\Auth\LoginController::middleware() {"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Auth\\LoginController::middleware() at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\app\\Http\\Controllers\\Auth\\LoginController.php:37)
[stacktrace]
#0 [internal function]: App\\Http\\Controllers\\Auth\\LoginController->__construct()
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#55 {main}
"} 
[2025-06-08 05:13:28] local.ERROR: Attempt to read property "name" on null (View: D:\FILE\website\PORTOFOLIO\SIPUS\resources\views\layouts\app.blade.php) (View: D:\FILE\website\PORTOFOLIO\SIPUS\resources\views\layouts\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Attempt to read property \"name\" on null (View: D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\resources\\views\\layouts\\app.blade.php) (View: D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\resources\\views\\layouts\\app.blade.php) at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\dde75b4ae15a2a7d3f22ac45a8a06bfd.php:52)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): Attempt to read property \"name\" on null (View: D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\resources\\views\\layouts\\app.blade.php) at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\dde75b4ae15a2a7d3f22ac45a8a06bfd.php:52)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ErrorException), 2)
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\9ec9201bb731e49e5c3756db5453cff4.php(104): Illuminate\\View\\View->render()
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\FILE\\\\website...')
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\FILE\\\\website...', Array)
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#64 {main}

[previous exception] [object] (ErrorException(code: 0): Attempt to read property \"name\" on null at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\dde75b4ae15a2a7d3f22ac45a8a06bfd.php:52)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'D:\\\\FILE\\\\website...', 52)
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\dde75b4ae15a2a7d3f22ac45a8a06bfd.php(52): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'D:\\\\FILE\\\\website...', 52)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\FILE\\\\website...')
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\FILE\\\\website...', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\9ec9201bb731e49e5c3756db5453cff4.php(104): Illuminate\\View\\View->render()
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\FILE\\\\website...')
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\FILE\\\\website...', Array)
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 {main}
"} 
[2025-06-08 05:15:16] local.ERROR: Attempt to read property "name" on null (View: D:\FILE\website\PORTOFOLIO\SIPUS\resources\views\layouts\app.blade.php) (View: D:\FILE\website\PORTOFOLIO\SIPUS\resources\views\layouts\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Attempt to read property \"name\" on null (View: D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\resources\\views\\layouts\\app.blade.php) (View: D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\resources\\views\\layouts\\app.blade.php) at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\dde75b4ae15a2a7d3f22ac45a8a06bfd.php:52)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#57 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): Attempt to read property \"name\" on null (View: D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\resources\\views\\layouts\\app.blade.php) at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\dde75b4ae15a2a7d3f22ac45a8a06bfd.php:52)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ErrorException), 2)
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\9ec9201bb731e49e5c3756db5453cff4.php(104): Illuminate\\View\\View->render()
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\FILE\\\\website...')
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\FILE\\\\website...', Array)
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#64 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#65 {main}

[previous exception] [object] (ErrorException(code: 0): Attempt to read property \"name\" on null at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\dde75b4ae15a2a7d3f22ac45a8a06bfd.php:52)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'D:\\\\FILE\\\\website...', 52)
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\dde75b4ae15a2a7d3f22ac45a8a06bfd.php(52): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'D:\\\\FILE\\\\website...', 52)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\FILE\\\\website...')
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\FILE\\\\website...', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\storage\\framework\\views\\9ec9201bb731e49e5c3756db5453cff4.php(104): Illuminate\\View\\View->render()
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\FILE\\\\website...')
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\FILE\\\\website...', Array)
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\FILE\\\\website...', Array)
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\FILE\\\\website...', Array)
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#69 {main}
"} 
[2025-06-08 05:21:21] local.ERROR: Target class [permission] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [permission] does not exist. at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('permission')
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#51 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"permission\" does not exist at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('permission')
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#52 {main}
"} 
[2025-06-08 05:21:22] local.ERROR: Target class [permission] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [permission] does not exist. at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('permission')
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#8 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#9 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"permission\" does not exist at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('permission')
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#9 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#10 {main}
"} 
[2025-06-08 05:21:22] local.ERROR: Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php:387) in D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php:322
Stack trace:
#0 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\FILE\\website...', 322)
#1 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(2, 'Cannot modify h...', 'D:\\FILE\\website...', 322)
#2 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php(401): Symfony\Component\HttpFoundation\Response->sendHeaders()
#4 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Symfony\Component\HttpFoundation\Response->send()
#5 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Contracts\Container\BindingResolutionException))
#6 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Contracts\Container\BindingResolutionException))
#7 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Contracts\Container\BindingResolutionException))
#8 {main}
  thrown {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:387) in D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\\\FILE\\\\website...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'D:\\\\FILE\\\\website...', 322)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#8 {main}
  thrown at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 {main}
"} 
[2025-06-08 05:21:31] local.ERROR: Target class [permission] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [permission] does not exist. at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('permission')
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#51 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"permission\" does not exist at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('permission')
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#52 {main}
"} 
[2025-06-08 05:21:31] local.ERROR: Target class [permission] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [permission] does not exist. at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('permission')
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#8 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#9 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"permission\" does not exist at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('permission')
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#9 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\FILE\\\\website...')
#10 {main}
"} 
[2025-06-08 05:21:32] local.ERROR: Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php:387) in D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php:322
Stack trace:
#0 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\FILE\\website...', 322)
#1 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(2, 'Cannot modify h...', 'D:\\FILE\\website...', 322)
#2 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php(401): Symfony\Component\HttpFoundation\Response->sendHeaders()
#4 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Symfony\Component\HttpFoundation\Response->send()
#5 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Contracts\Container\BindingResolutionException))
#6 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Contracts\Container\BindingResolutionException))
#7 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Contracts\Container\BindingResolutionException))
#8 {main}
  thrown {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:387) in D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\\\FILE\\\\website...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'D:\\\\FILE\\\\website...', 322)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#8 {main}
  thrown at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 {main}
"} 
[2025-06-08 05:26:27] local.ERROR: Target class [permission] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [permission] does not exist. at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('permission')
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"permission\" does not exist at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('permission')
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 {main}
"} 
[2025-06-08 05:26:29] local.ERROR: Target class [permission] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [permission] does not exist. at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('permission')
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#8 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"permission\" does not exist at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('permission')
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#9 {main}
"} 
[2025-06-08 05:26:29] local.ERROR: Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php:387) in D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php:322
Stack trace:
#0 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\FILE\\website...', 322)
#1 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(2, 'Cannot modify h...', 'D:\\FILE\\website...', 322)
#2 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php(401): Symfony\Component\HttpFoundation\Response->sendHeaders()
#4 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Symfony\Component\HttpFoundation\Response->send()
#5 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Contracts\Container\BindingResolutionException))
#6 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Contracts\Container\BindingResolutionException))
#7 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Contracts\Container\BindingResolutionException))
#8 {main}
  thrown {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:387) in D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\\\FILE\\\\website...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'D:\\\\FILE\\\\website...', 322)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#8 {main}
  thrown at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 {main}
"} 
[2025-06-08 05:39:15] local.ERROR: Target class [permission] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [permission] does not exist. at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('permission')
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"permission\" does not exist at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('permission')
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 {main}
"} 
[2025-06-08 05:39:16] local.ERROR: Target class [permission] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [permission] does not exist. at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('permission')
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#8 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"permission\" does not exist at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('permission')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('permission')
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('permission', Array, true)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('permission', Array)
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('permission', Array)
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('permission')
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#9 {main}
"} 
[2025-06-08 05:39:17] local.ERROR: Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php:387) in D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php:322
Stack trace:
#0 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\FILE\\website...', 322)
#1 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(2, 'Cannot modify h...', 'D:\\FILE\\website...', 322)
#2 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\symfony\http-foundation\Response.php(401): Symfony\Component\HttpFoundation\Response->sendHeaders()
#4 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Symfony\Component\HttpFoundation\Response->send()
#5 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Contracts\Container\BindingResolutionException))
#6 D:\FILE\website\PORTOFOLIO\SIPUS\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Contracts\Container\BindingResolutionException))
#7 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Contracts\Container\BindingResolutionException))
#8 {main}
  thrown {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:387) in D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\\\FILE\\\\website...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'D:\\\\FILE\\\\website...', 322)
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#8 {main}
  thrown at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 {main}
"} 
[2025-06-08 05:39:57] local.ERROR: You need to install the imagick extension to use this back end {"exception":"[object] (BaconQrCode\\Exception\\RuntimeException(code: 0): You need to install the imagick extension to use this back end at D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\bacon\\bacon-qr-code\\src\\Renderer\\Image\\ImagickImageBackEnd.php:64)
[stacktrace]
#0 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\simplesoftwareio\\simple-qrcode\\src\\Generator.php(464): BaconQrCode\\Renderer\\Image\\ImagickImageBackEnd->__construct('png')
#1 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\simplesoftwareio\\simple-qrcode\\src\\Generator.php(440): SimpleSoftwareIO\\QrCode\\Generator->getFormatter()
#2 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\simplesoftwareio\\simple-qrcode\\src\\Generator.php(171): SimpleSoftwareIO\\QrCode\\Generator->getRenderer()
#3 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\database\\seeders\\MemberSeeder.php(76): SimpleSoftwareIO\\QrCode\\Generator->generate('{\"type\":\"member...')
#4 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\MemberSeeder->run()
#5 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#10 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#11 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#12 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#13 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#14 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#15 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#20 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\FILE\\website\\PORTOFOLIO\\SIPUS\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#28 {main}
"} 
