<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Book extends Model
{
    protected $fillable = [
        'title',
        'author',
        'publisher',
        'publication_year',
        'isbn',
        'total_copies',
        'available_copies',
        'shelf_location',
        'category_id',
        'description',
        'cover_image',
        'qr_code',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'publication_year' => 'integer',
        'total_copies' => 'integer',
        'available_copies' => 'integer'
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function borrowings(): HasMany
    {
        return $this->hasMany(Borrowing::class);
    }
}
