<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Borrowing;
use App\Models\Book;
use App\Models\Member;
use Carbon\Carbon;

class BorrowingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $borrowings = [
            [
                'borrowing_code' => 'BRW' . date('Ymd') . '0001',
                'member_id' => 1, // <PERSON>
                'book_id' => 1, // Laravel book
                'user_id' => 1, // Admin
                'borrow_date' => Carbon::now()->subDays(5),
                'due_date' => Carbon::now()->addDays(2),
                'status' => 'borrowed',
                'notes' => 'Peminjaman normal'
            ],
            [
                'borrowing_code' => 'BRW' . date('Ymd') . '0002',
                'member_id' => 2, // Siti Nurhaliza
                'book_id' => 2, // Manajemen Keuangan
                'user_id' => 1, // Admin
                'borrow_date' => Carbon::now()->subDays(10),
                'due_date' => Carbon::now()->subDays(3), // Overdue
                'status' => 'overdue',
                'notes' => 'Peminjaman terlambat'
            ],
            [
                'borrowing_code' => 'BRW' . date('Ymd') . '0003',
                'member_id' => 3, // Dr. Budi
                'book_id' => 3, // Laskar Pelangi
                'user_id' => 1, // Admin
                'borrow_date' => Carbon::now()->subDays(3),
                'due_date' => Carbon::now()->addDays(4),
                'status' => 'borrowed',
                'notes' => 'Peminjaman dosen'
            ]
        ];

        foreach ($borrowings as $borrowingData) {
            $borrowing = Borrowing::create($borrowingData);

            // Update book available copies
            $book = Book::find($borrowingData['book_id']);
            $book->decrement('available_copies');
        }
    }
}
