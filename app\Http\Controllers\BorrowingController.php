<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Borrowing;
use App\Models\Book;
use App\Models\Member;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BorrowingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Borrowing::with(['member', 'book', 'user']);

        // Search functionality
        if ($request->has('search') && $request->search != '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('borrowing_code', 'like', "%{$search}%")
                  ->orWhereHas('member', function($member) use ($search) {
                      $member->where('name', 'like', "%{$search}%")
                             ->orWhere('member_id', 'like', "%{$search}%");
                  })
                  ->orWhereHas('book', function($book) use ($search) {
                      $book->where('title', 'like', "%{$search}%")
                           ->orWhere('isbn', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from != '') {
            $query->whereDate('borrow_date', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to != '') {
            $query->whereDate('borrow_date', '<=', $request->date_to);
        }

        $borrowings = $query->orderBy('created_at', 'desc')->paginate(10);

        return view('borrowings.index', compact('borrowings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $members = Member::where('is_active', true)
                        ->where('expired_date', '>', now())
                        ->orderBy('name')
                        ->get();

        $books = Book::where('is_active', true)
                    ->where('available_copies', '>', 0)
                    ->with('category')
                    ->orderBy('title')
                    ->get();

        return view('borrowings.create', compact('members', 'books'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'member_id' => 'required|exists:members,id',
            'book_id' => 'required|exists:books,id',
            'borrow_date' => 'required|date',
            'due_date' => 'required|date|after:borrow_date',
            'notes' => 'nullable|string'
        ]);

        // Check if member is active and not expired
        $member = Member::findOrFail($request->member_id);
        if (!$member->is_active || $member->expired_date < now()) {
            return back()->withErrors(['member_id' => 'Anggota tidak aktif atau sudah kadaluarsa.']);
        }

        // Check if book is available
        $book = Book::findOrFail($request->book_id);
        if (!$book->is_active || $book->available_copies <= 0) {
            return back()->withErrors(['book_id' => 'Buku tidak tersedia untuk dipinjam.']);
        }

        // Check if member has overdue books
        $overdueCount = Borrowing::where('member_id', $request->member_id)
                                ->where('status', 'borrowed')
                                ->where('due_date', '<', now())
                                ->count();

        if ($overdueCount > 0) {
            return back()->withErrors(['member_id' => 'Anggota memiliki buku yang terlambat dikembalikan.']);
        }

        // Check borrowing limit (max 3 books)
        $activeBorrowings = Borrowing::where('member_id', $request->member_id)
                                   ->where('status', 'borrowed')
                                   ->count();

        if ($activeBorrowings >= 3) {
            return back()->withErrors(['member_id' => 'Anggota sudah mencapai batas maksimal peminjaman (3 buku).']);
        }

        DB::transaction(function () use ($request, $book) {
            // Create borrowing record
            $borrowing = Borrowing::create([
                'borrowing_code' => $this->generateBorrowingCode(),
                'member_id' => $request->member_id,
                'book_id' => $request->book_id,
                'user_id' => Auth::id(),
                'borrow_date' => $request->borrow_date,
                'due_date' => $request->due_date,
                'status' => 'borrowed',
                'notes' => $request->notes
            ]);

            // Update book available copies
            $book->decrement('available_copies');
        });

        return redirect()->route('borrowings.index')
            ->with('success', 'Peminjaman berhasil dicatat.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Borrowing $borrowing)
    {
        $borrowing->load(['member', 'book.category', 'user', 'returnBook']);
        return view('borrowings.show', compact('borrowing'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Borrowing $borrowing)
    {
        // Only allow editing if not returned
        if ($borrowing->status == 'returned') {
            return redirect()->route('borrowings.index')
                ->with('error', 'Peminjaman yang sudah dikembalikan tidak dapat diedit.');
        }

        $members = Member::where('is_active', true)
                        ->where('expired_date', '>', now())
                        ->orderBy('name')
                        ->get();

        $books = Book::where('is_active', true)
                    ->where(function($query) use ($borrowing) {
                        $query->where('available_copies', '>', 0)
                              ->orWhere('id', $borrowing->book_id);
                    })
                    ->with('category')
                    ->orderBy('title')
                    ->get();

        return view('borrowings.edit', compact('borrowing', 'members', 'books'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Borrowing $borrowing)
    {
        // Only allow updating if not returned
        if ($borrowing->status == 'returned') {
            return redirect()->route('borrowings.index')
                ->with('error', 'Peminjaman yang sudah dikembalikan tidak dapat diedit.');
        }

        $request->validate([
            'due_date' => 'required|date|after:borrow_date',
            'notes' => 'nullable|string'
        ]);

        $borrowing->update([
            'due_date' => $request->due_date,
            'notes' => $request->notes
        ]);

        return redirect()->route('borrowings.index')
            ->with('success', 'Data peminjaman berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Borrowing $borrowing)
    {
        // Only allow deletion if not returned
        if ($borrowing->status == 'returned') {
            return redirect()->route('borrowings.index')
                ->with('error', 'Peminjaman yang sudah dikembalikan tidak dapat dihapus.');
        }

        DB::transaction(function () use ($borrowing) {
            // Return book stock
            $borrowing->book->increment('available_copies');

            // Delete borrowing record
            $borrowing->delete();
        });

        return redirect()->route('borrowings.index')
            ->with('success', 'Data peminjaman berhasil dihapus.');
    }

    /**
     * Extend due date
     */
    public function extend(Request $request, Borrowing $borrowing)
    {
        $request->validate([
            'new_due_date' => 'required|date|after:due_date'
        ]);

        // Check if already extended (max 1 extension)
        if ($borrowing->due_date != Carbon::parse($borrowing->borrow_date)->addDays(7)) {
            return back()->with('error', 'Peminjaman sudah pernah diperpanjang.');
        }

        $borrowing->update([
            'due_date' => $request->new_due_date,
            'notes' => $borrowing->notes . "\nDiperpanjang pada " . now()->format('d/m/Y H:i')
        ]);

        return back()->with('success', 'Masa peminjaman berhasil diperpanjang.');
    }

    /**
     * Generate unique borrowing code
     */
    private function generateBorrowingCode()
    {
        $prefix = 'BRW';
        $date = date('Ymd');

        $lastBorrowing = Borrowing::where('borrowing_code', 'like', $prefix . $date . '%')
                                ->orderBy('borrowing_code', 'desc')
                                ->first();

        if ($lastBorrowing) {
            $lastNumber = (int) substr($lastBorrowing->borrowing_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Update overdue status
     */
    public function updateOverdueStatus()
    {
        $overdueCount = Borrowing::where('status', 'borrowed')
                               ->where('due_date', '<', now())
                               ->update(['status' => 'overdue']);

        return response()->json(['updated' => $overdueCount]);
    }
}
