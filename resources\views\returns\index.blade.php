@extends('layouts.app')

@section('title', 'Manajemen Pengembalian')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h4>Daftar Pengembalian Buku</h4>
        <div class="card-header-action">
          <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#quickReturnModal">
            <i class="fas fa-qrcode"></i> Quick Return
          </button>
          <a href="{{ route('returns.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Pengembalian
          </a>
        </div>
      </div>
      <div class="card-body">
        <!-- Search and Filter -->
        <form method="GET" action="{{ route('returns.index') }}" class="mb-3">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <input type="text" name="search" class="form-control" placeholder="Cari kode, anggota, buku..." value="{{ request('search') }}">
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <select name="condition" class="form-control">
                  <option value="">Semua Kondisi</option>
                  <option value="good" {{ request('condition') == 'good' ? 'selected' : '' }}>Baik</option>
                  <option value="damaged" {{ request('condition') == 'damaged' ? 'selected' : '' }}>Rusak</option>
                  <option value="lost" {{ request('condition') == 'lost' ? 'selected' : '' }}>Hilang</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <input type="date" name="date_from" class="form-control" placeholder="Dari tanggal" value="{{ request('date_from') }}">
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <input type="date" name="date_to" class="form-control" placeholder="Sampai tanggal" value="{{ request('date_to') }}">
              </div>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-info">
                <i class="fas fa-search"></i> Cari
              </button>
            </div>
            <div class="col-md-1 text-right">
              @if(request('search') || request('condition') || request('date_from') || request('date_to'))
                <a href="{{ route('returns.index') }}" class="btn btn-secondary">
                  <i class="fas fa-times"></i>
                </a>
              @endif
            </div>
          </div>
        </form>

        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Kode Pinjam</th>
                <th>Anggota</th>
                <th>Buku</th>
                <th>Tgl Kembali</th>
                <th>Terlambat</th>
                <th>Kondisi</th>
                <th>Denda</th>
                <th>Petugas</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody>
              @forelse($returns as $return)
              <tr>
                <td>
                  <strong>{{ $return->borrowing->borrowing_code }}</strong>
                </td>
                <td>
                  <strong>{{ $return->borrowing->member->name }}</strong><br>
                  <small class="text-muted">{{ $return->borrowing->member->member_id }}</small>
                </td>
                <td>
                  <strong>{{ $return->borrowing->book->title }}</strong><br>
                  <small class="text-muted">{{ $return->borrowing->book->author }}</small>
                </td>
                <td>{{ $return->return_date->format('d/m/Y') }}</td>
                <td>
                  @if($return->days_late > 0)
                    <span class="badge badge-danger">{{ $return->days_late }} hari</span>
                  @else
                    <span class="badge badge-success">Tepat waktu</span>
                  @endif
                </td>
                <td>
                  @if($return->condition == 'good')
                    <span class="badge badge-success">Baik</span>
                  @elseif($return->condition == 'damaged')
                    <span class="badge badge-warning">Rusak</span>
                  @else
                    <span class="badge badge-danger">Hilang</span>
                  @endif
                </td>
                <td>
                  @if($return->fine_amount > 0)
                    <span class="text-danger">Rp {{ number_format($return->fine_amount, 0, ',', '.') }}</span>
                  @else
                    <span class="text-success">-</span>
                  @endif
                </td>
                <td>{{ $return->user->name }}</td>
                <td>
                  <div class="btn-group" role="group">
                    <a href="{{ route('returns.show', $return) }}" class="btn btn-info btn-sm" title="Detail">
                      <i class="fas fa-eye"></i>
                    </a>
                    <a href="{{ route('returns.edit', $return) }}" class="btn btn-warning btn-sm" title="Edit">
                      <i class="fas fa-edit"></i>
                    </a>
                    <form action="{{ route('returns.destroy', $return) }}" method="POST" class="d-inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus data pengembalian ini? Ini akan mengembalikan status peminjaman.')">
                      @csrf
                      @method('DELETE')
                      <button type="submit" class="btn btn-danger btn-sm" title="Hapus">
                        <i class="fas fa-trash"></i>
                      </button>
                    </form>
                  </div>
                </td>
              </tr>
              @empty
              <tr>
                <td colspan="9" class="text-center">
                  @if(request('search') || request('condition') || request('date_from') || request('date_to'))
                    Tidak ada pengembalian yang sesuai dengan pencarian.
                  @else
                    Belum ada data pengembalian.
                  @endif
                </td>
              </tr>
              @endforelse
            </tbody>
          </table>
        </div>
        
        <!-- Pagination -->
        <div class="d-flex justify-content-center">
          {{ $returns->appends(request()->query())->links() }}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Quick Return Modal -->
<div class="modal fade" id="quickReturnModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Quick Return - Scan QR atau Masukkan Kode</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="borrowing_code">Kode Peminjaman</label>
          <input type="text" class="form-control" id="borrowing_code" placeholder="Masukkan atau scan kode peminjaman">
        </div>
        
        <div id="borrowing-info" style="display: none;">
          <div class="alert alert-info">
            <h6>Informasi Peminjaman:</h6>
            <div class="row">
              <div class="col-md-6">
                <strong>Anggota:</strong> <span id="info-member"></span><br>
                <strong>Buku:</strong> <span id="info-book"></span><br>
              </div>
              <div class="col-md-6">
                <strong>Tanggal Pinjam:</strong> <span id="info-borrow-date"></span><br>
                <strong>Jatuh Tempo:</strong> <span id="info-due-date"></span><br>
                <strong>Keterlambatan:</strong> <span id="info-late"></span>
              </div>
            </div>
          </div>
          
          <form id="quickReturnForm" action="{{ route('returns.store') }}" method="POST">
            @csrf
            <input type="hidden" id="quick_borrowing_id" name="borrowing_id">
            
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="quick_return_date">Tanggal Kembali</label>
                  <input type="date" class="form-control" id="quick_return_date" name="return_date" value="{{ date('Y-m-d') }}" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="quick_condition">Kondisi Buku</label>
                  <select class="form-control" id="quick_condition" name="condition" required>
                    <option value="good">Baik</option>
                    <option value="damaged">Rusak</option>
                    <option value="lost">Hilang</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label for="quick_notes">Catatan</label>
              <textarea class="form-control" id="quick_notes" name="notes" rows="2"></textarea>
            </div>
          </form>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
        <button type="button" class="btn btn-primary" id="searchBorrowingBtn">Cari</button>
        <button type="submit" form="quickReturnForm" class="btn btn-success" id="processReturnBtn" style="display: none;">Proses Pengembalian</button>
      </div>
    </div>
  </div>
</div>
@endsection

@push('script')
<script>
$(document).ready(function() {
  $('#searchBorrowingBtn').click(function() {
    let code = $('#borrowing_code').val().trim();
    if (!code) {
      alert('Masukkan kode peminjaman');
      return;
    }
    
    $.post('{{ route("returns.quickReturn") }}', {
      _token: '{{ csrf_token() }}',
      code: code
    })
    .done(function(data) {
      if (data.success) {
        $('#info-member').text(data.member.name + ' (' + data.member.member_id + ')');
        $('#info-book').text(data.book.title);
        $('#info-borrow-date').text(new Date(data.borrowing.borrow_date).toLocaleDateString('id-ID'));
        $('#info-due-date').text(new Date(data.borrowing.due_date).toLocaleDateString('id-ID'));
        $('#info-late').text(data.days_late > 0 ? data.days_late + ' hari' : 'Tidak terlambat');
        
        $('#quick_borrowing_id').val(data.borrowing.id);
        $('#borrowing-info').show();
        $('#searchBorrowingBtn').hide();
        $('#processReturnBtn').show();
      } else {
        alert(data.message);
      }
    })
    .fail(function() {
      alert('Terjadi kesalahan saat mencari data peminjaman.');
    });
  });
  
  $('#quickReturnModal').on('hidden.bs.modal', function() {
    $('#borrowing_code').val('');
    $('#borrowing-info').hide();
    $('#searchBorrowingBtn').show();
    $('#processReturnBtn').hide();
  });
});
</script>
@endpush
