@extends('layouts.app')

@section('title', 'Manajemen Peminjaman')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h4>Daftar Peminjaman Buku</h4>
        <div class="card-header-action">
          <button type="button" class="btn btn-warning btn-sm" onclick="updateOverdueStatus()">
            <i class="fas fa-sync"></i> Update Status Terlambat
          </button>
          <a href="{{ route('borrowings.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Peminjaman
          </a>
        </div>
      </div>
      <div class="card-body">
        <!-- Search and Filter -->
        <form method="GET" action="{{ route('borrowings.index') }}" class="mb-3">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <input type="text" name="search" class="form-control" placeholder="Cari kode, anggota, buku..." value="{{ request('search') }}">
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <select name="status" class="form-control">
                  <option value="">Semua Status</option>
                  <option value="borrowed" {{ request('status') == 'borrowed' ? 'selected' : '' }}>Dipinjam</option>
                  <option value="returned" {{ request('status') == 'returned' ? 'selected' : '' }}>Dikembalikan</option>
                  <option value="overdue" {{ request('status') == 'overdue' ? 'selected' : '' }}>Terlambat</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <input type="date" name="date_from" class="form-control" placeholder="Dari tanggal" value="{{ request('date_from') }}">
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <input type="date" name="date_to" class="form-control" placeholder="Sampai tanggal" value="{{ request('date_to') }}">
              </div>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-info">
                <i class="fas fa-search"></i> Cari
              </button>
            </div>
            <div class="col-md-1 text-right">
              @if(request('search') || request('status') || request('date_from') || request('date_to'))
                <a href="{{ route('borrowings.index') }}" class="btn btn-secondary">
                  <i class="fas fa-times"></i>
                </a>
              @endif
            </div>
          </div>
        </form>

        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Kode</th>
                <th>Anggota</th>
                <th>Buku</th>
                <th>Tgl Pinjam</th>
                <th>Jatuh Tempo</th>
                <th>Status</th>
                <th>Petugas</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody>
              @forelse($borrowings as $borrowing)
              <tr>
                <td>
                  <strong>{{ $borrowing->borrowing_code }}</strong>
                </td>
                <td>
                  <strong>{{ $borrowing->member->name }}</strong><br>
                  <small class="text-muted">{{ $borrowing->member->member_id }}</small>
                </td>
                <td>
                  <strong>{{ $borrowing->book->title }}</strong><br>
                  <small class="text-muted">{{ $borrowing->book->author }}</small>
                </td>
                <td>{{ $borrowing->borrow_date->format('d/m/Y') }}</td>
                <td>
                  {{ $borrowing->due_date->format('d/m/Y') }}
                  @if($borrowing->status == 'borrowed' && $borrowing->due_date < now())
                    <br><small class="text-danger">{{ $borrowing->due_date->diffForHumans() }}</small>
                  @endif
                </td>
                <td>
                  @if($borrowing->status == 'borrowed')
                    @if($borrowing->due_date < now())
                      <span class="badge badge-danger">Terlambat</span>
                    @else
                      <span class="badge badge-warning">Dipinjam</span>
                    @endif
                  @elseif($borrowing->status == 'returned')
                    <span class="badge badge-success">Dikembalikan</span>
                  @else
                    <span class="badge badge-danger">Terlambat</span>
                  @endif
                </td>
                <td>{{ $borrowing->user->name }}</td>
                <td>
                  <div class="btn-group" role="group">
                    <a href="{{ route('borrowings.show', $borrowing) }}" class="btn btn-info btn-sm" title="Detail">
                      <i class="fas fa-eye"></i>
                    </a>
                    @if($borrowing->status != 'returned')
                      <a href="{{ route('borrowings.edit', $borrowing) }}" class="btn btn-warning btn-sm" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>
                      @if($borrowing->status == 'borrowed')
                        <button type="button" class="btn btn-secondary btn-sm" title="Perpanjang" onclick="showExtendModal({{ $borrowing->id }}, '{{ $borrowing->due_date->format('Y-m-d') }}')">
                          <i class="fas fa-calendar-plus"></i>
                        </button>
                      @endif
                      <form action="{{ route('borrowings.destroy', $borrowing) }}" method="POST" class="d-inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus data peminjaman ini?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger btn-sm" title="Hapus">
                          <i class="fas fa-trash"></i>
                        </button>
                      </form>
                    @endif
                  </div>
                </td>
              </tr>
              @empty
              <tr>
                <td colspan="8" class="text-center">
                  @if(request('search') || request('status') || request('date_from') || request('date_to'))
                    Tidak ada peminjaman yang sesuai dengan pencarian.
                  @else
                    Belum ada data peminjaman.
                  @endif
                </td>
              </tr>
              @endforelse
            </tbody>
          </table>
        </div>
        
        <!-- Pagination -->
        <div class="d-flex justify-content-center">
          {{ $borrowings->appends(request()->query())->links() }}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Extend Modal -->
<div class="modal fade" id="extendModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Perpanjang Masa Peminjaman</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <form id="extendForm" method="POST">
        @csrf
        <div class="modal-body">
          <div class="form-group">
            <label for="new_due_date">Jatuh Tempo Baru</label>
            <input type="date" class="form-control" id="new_due_date" name="new_due_date" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-primary">Perpanjang</button>
        </div>
      </form>
    </div>
  </div>
</div>
@endsection

@push('script')
<script>
function showExtendModal(borrowingId, currentDueDate) {
  $('#extendForm').attr('action', '/borrowings/' + borrowingId + '/extend');
  
  // Set minimum date to current due date + 1 day
  let minDate = new Date(currentDueDate);
  minDate.setDate(minDate.getDate() + 1);
  $('#new_due_date').attr('min', minDate.toISOString().split('T')[0]);
  
  // Set default to 7 days from current due date
  let defaultDate = new Date(currentDueDate);
  defaultDate.setDate(defaultDate.getDate() + 7);
  $('#new_due_date').val(defaultDate.toISOString().split('T')[0]);
  
  $('#extendModal').modal('show');
}

function updateOverdueStatus() {
  $.post('{{ route("borrowings.updateOverdue") }}', {
    _token: '{{ csrf_token() }}'
  })
  .done(function(data) {
    if (data.updated > 0) {
      alert('Status ' + data.updated + ' peminjaman berhasil diperbarui menjadi terlambat.');
      location.reload();
    } else {
      alert('Tidak ada peminjaman yang perlu diperbarui statusnya.');
    }
  })
  .fail(function() {
    alert('Terjadi kesalahan saat memperbarui status.');
  });
}
</script>
@endpush
