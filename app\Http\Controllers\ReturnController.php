<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ReturnBook;
use App\Models\Borrowing;
use App\Models\Fine;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ReturnController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ReturnBook::with(['borrowing.member', 'borrowing.book', 'user']);

        // Search functionality
        if ($request->has('search') && $request->search != '') {
            $search = $request->search;
            $query->whereHas('borrowing', function($q) use ($search) {
                $q->where('borrowing_code', 'like', "%{$search}%")
                  ->orWhereHas('member', function($member) use ($search) {
                      $member->where('name', 'like', "%{$search}%")
                             ->orWhere('member_id', 'like', "%{$search}%");
                  })
                  ->orWhereHas('book', function($book) use ($search) {
                      $book->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by condition
        if ($request->has('condition') && $request->condition != '') {
            $query->where('condition', $request->condition);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from != '') {
            $query->whereDate('return_date', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to != '') {
            $query->whereDate('return_date', '<=', $request->date_to);
        }

        $returns = $query->orderBy('created_at', 'desc')->paginate(10);

        return view('returns.index', compact('returns'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $activeBorrowings = Borrowing::with(['member', 'book'])
                                   ->where('status', 'borrowed')
                                   ->orWhere('status', 'overdue')
                                   ->orderBy('due_date', 'asc')
                                   ->get();

        return view('returns.create', compact('activeBorrowings'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'borrowing_id' => 'required|exists:borrowings,id',
            'return_date' => 'required|date',
            'condition' => 'required|in:good,damaged,lost',
            'notes' => 'nullable|string'
        ]);

        $borrowing = Borrowing::findOrFail($request->borrowing_id);

        // Check if already returned
        if ($borrowing->status == 'returned') {
            return back()->withErrors(['borrowing_id' => 'Buku sudah dikembalikan sebelumnya.']);
        }

        $returnDate = Carbon::parse($request->return_date);
        $dueDate = Carbon::parse($borrowing->due_date);

        // Calculate late days
        $daysLate = $returnDate->gt($dueDate) ? $returnDate->diffInDays($dueDate) : 0;

        // Calculate fine (Rp 1000 per day)
        $fineAmount = $daysLate * 1000;

        // Additional fine for damaged/lost books
        if ($request->condition == 'damaged') {
            $fineAmount += 10000; // Rp 10,000 for damaged book
        } elseif ($request->condition == 'lost') {
            $fineAmount += 50000; // Rp 50,000 for lost book
        }

        DB::transaction(function () use ($request, $borrowing, $returnDate, $daysLate, $fineAmount) {
            // Create return record
            $return = ReturnBook::create([
                'borrowing_id' => $request->borrowing_id,
                'user_id' => Auth::id(),
                'return_date' => $request->return_date,
                'days_late' => $daysLate,
                'fine_amount' => $fineAmount,
                'condition' => $request->condition,
                'notes' => $request->notes
            ]);

            // Update borrowing status
            $borrowing->update([
                'status' => 'returned',
                'return_date' => $request->return_date
            ]);

            // Return book stock (only if not lost)
            if ($request->condition != 'lost') {
                $borrowing->book->increment('available_copies');
            }

            // Create fine record if there's a fine
            if ($fineAmount > 0) {
                Fine::create([
                    'member_id' => $borrowing->member_id,
                    'borrowing_id' => $borrowing->id,
                    'amount' => $fineAmount,
                    'reason' => $this->getFineReason($daysLate, $request->condition),
                    'status' => 'unpaid',
                    'due_date' => now()->addDays(30) // 30 days to pay fine
                ]);
            }
        });

        return redirect()->route('returns.index')
            ->with('success', 'Pengembalian berhasil dicatat.' . ($fineAmount > 0 ? ' Denda: Rp ' . number_format($fineAmount, 0, ',', '.') : ''));
    }

    /**
     * Display the specified resource.
     */
    public function show(ReturnBook $return)
    {
        $return->load(['borrowing.member', 'borrowing.book.category', 'user']);
        return view('returns.show', compact('return'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ReturnBook $return)
    {
        return view('returns.edit', compact('return'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ReturnBook $return)
    {
        $request->validate([
            'return_date' => 'required|date',
            'condition' => 'required|in:good,damaged,lost',
            'notes' => 'nullable|string'
        ]);

        $borrowing = $return->borrowing;
        $returnDate = Carbon::parse($request->return_date);
        $dueDate = Carbon::parse($borrowing->due_date);

        // Calculate late days
        $daysLate = $returnDate->gt($dueDate) ? $returnDate->diffInDays($dueDate) : 0;

        // Calculate fine
        $fineAmount = $daysLate * 1000;

        if ($request->condition == 'damaged') {
            $fineAmount += 10000;
        } elseif ($request->condition == 'lost') {
            $fineAmount += 50000;
        }

        DB::transaction(function () use ($request, $return, $borrowing, $returnDate, $daysLate, $fineAmount) {
            // Update return record
            $return->update([
                'return_date' => $request->return_date,
                'days_late' => $daysLate,
                'fine_amount' => $fineAmount,
                'condition' => $request->condition,
                'notes' => $request->notes
            ]);

            // Update borrowing return date
            $borrowing->update([
                'return_date' => $request->return_date
            ]);

            // Update fine if exists
            $fine = Fine::where('borrowing_id', $borrowing->id)->first();
            if ($fine) {
                $fine->update([
                    'amount' => $fineAmount,
                    'reason' => $this->getFineReason($daysLate, $request->condition)
                ]);
            } elseif ($fineAmount > 0) {
                Fine::create([
                    'member_id' => $borrowing->member_id,
                    'borrowing_id' => $borrowing->id,
                    'amount' => $fineAmount,
                    'reason' => $this->getFineReason($daysLate, $request->condition),
                    'status' => 'unpaid',
                    'due_date' => now()->addDays(30)
                ]);
            }
        });

        return redirect()->route('returns.index')
            ->with('success', 'Data pengembalian berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ReturnBook $return)
    {
        DB::transaction(function () use ($return) {
            $borrowing = $return->borrowing;

            // Revert borrowing status
            $borrowing->update([
                'status' => $borrowing->due_date < now() ? 'overdue' : 'borrowed',
                'return_date' => null
            ]);

            // Revert book stock (if not lost)
            if ($return->condition != 'lost') {
                $borrowing->book->decrement('available_copies');
            }

            // Delete related fine
            Fine::where('borrowing_id', $borrowing->id)->delete();

            // Delete return record
            $return->delete();
        });

        return redirect()->route('returns.index')
            ->with('success', 'Data pengembalian berhasil dihapus.');
    }

    /**
     * Get fine reason text
     */
    private function getFineReason($daysLate, $condition)
    {
        $reasons = [];

        if ($daysLate > 0) {
            $reasons[] = "Keterlambatan {$daysLate} hari (Rp " . number_format($daysLate * 1000, 0, ',', '.') . ")";
        }

        if ($condition == 'damaged') {
            $reasons[] = "Buku rusak (Rp 10.000)";
        } elseif ($condition == 'lost') {
            $reasons[] = "Buku hilang (Rp 50.000)";
        }

        return implode(', ', $reasons);
    }

    /**
     * Quick return by scanning QR or entering code
     */
    public function quickReturn(Request $request)
    {
        $request->validate([
            'code' => 'required|string'
        ]);

        $borrowing = Borrowing::where('borrowing_code', $request->code)
                             ->where('status', '!=', 'returned')
                             ->with(['member', 'book'])
                             ->first();

        if (!$borrowing) {
            return response()->json([
                'success' => false,
                'message' => 'Kode peminjaman tidak ditemukan atau sudah dikembalikan.'
            ]);
        }

        return response()->json([
            'success' => true,
            'borrowing' => $borrowing,
            'member' => $borrowing->member,
            'book' => $borrowing->book,
            'days_late' => now()->gt($borrowing->due_date) ? now()->diffInDays($borrowing->due_date) : 0
        ]);
    }
}
