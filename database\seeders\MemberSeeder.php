<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Member;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class MemberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $members = [
            [
                'member_id' => 'MHS20250001',
                'name' => '<PERSON> Rizki',
                'nim_nip' => '2021001',
                'email' => '<EMAIL>',
                'phone' => '081234567890',
                'address' => 'Jl. Merdeka No. 123, Jakarta',
                'status' => 'student',
                'join_date' => '2025-01-01',
                'expired_date' => '2029-01-01',
                'is_active' => true
            ],
            [
                'member_id' => 'MHS20250002',
                'name' => 'Siti Nurhaliza',
                'nim_nip' => '2021002',
                'email' => '<EMAIL>',
                'phone' => '081234567891',
                'address' => 'J<PERSON>. <PERSON>dirman No. 456, Jakarta',
                'status' => 'student',
                'join_date' => '2025-01-01',
                'expired_date' => '2029-01-01',
                'is_active' => true
            ],
            [
                'member_id' => 'DSN20250001',
                'name' => 'Dr. Budi Santoso',
                'nim_nip' => '198501001',
                'email' => '<EMAIL>',
                'phone' => '081234567892',
                'address' => 'Jl. Thamrin No. 789, Jakarta',
                'status' => 'lecturer',
                'join_date' => '2025-01-01',
                'expired_date' => '2026-01-01',
                'is_active' => true
            ],
            [
                'member_id' => 'STF20250001',
                'name' => 'Rina Wati',
                'nim_nip' => '199001001',
                'email' => '<EMAIL>',
                'phone' => '081234567893',
                'address' => 'Jl. Gatot Subroto No. 321, Jakarta',
                'status' => 'staff',
                'join_date' => '2025-01-01',
                'expired_date' => '2026-01-01',
                'is_active' => true
            ]
        ];

        foreach ($members as $memberData) {
            // Generate QR Code (temporarily disabled due to imagick requirement)
            // $qrData = json_encode([
            //     'type' => 'member',
            //     'member_id' => $memberData['member_id'],
            //     'name' => $memberData['name'],
            //     'nim_nip' => $memberData['nim_nip']
            // ]);
            // $memberData['qr_code'] = base64_encode(QrCode::format('png')->size(200)->generate($qrData));

            Member::create($memberData);
        }
    }
}
